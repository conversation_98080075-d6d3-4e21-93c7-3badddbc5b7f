# Configuration for the Temporal workflow example
$schema: ../../schema/mcp-agent.config.schema.json

# Set the execution engine to Temporal
execution_engine: "temporal"

# Temporal settings
temporal:
  host: "localhost:7233" # Default Temporal server address
  namespace: "default" # Default Temporal namespace
  task_queue: "mcp-agent" # Task queue for workflows and activities
  max_concurrent_activities: 10 # Maximum number of concurrent activities

# Logger settings
logger:
  transports: [console, file]
  level: debug
  progress_display: false
  path_settings:
    path_pattern: "logs/mcp-agent-{unique_id}.jsonl"
    unique_id: "timestamp" # Options: "timestamp" or "session_id"
    timestamp_format: "%Y%m%d_%H%M%S"

mcp:
  servers:
    fetch:
      command: "uvx"
      args: ["mcp-server-fetch"]
      description: "Fetch content at URLs from the world wide web"
    filesystem:
      command: "npx"
      args: [
          "-y",
          "@modelcontextprotocol/server-filesystem",
          # Current directory will be added by the code
        ]
      description: "Read and write files on the filesystem"

openai:
  # Secrets (API keys, etc.) are stored in an mcp_agent.secrets.yaml file which can be gitignored
  #  default_model: "o3-mini"
  default_model: "Qwen/Qwen3-8B"
